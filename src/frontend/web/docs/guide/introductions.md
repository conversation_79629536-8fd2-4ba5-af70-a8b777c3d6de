# 简介

AI 小鲸 (AI Blueking) 是一个智能对话组件，旨在为您的应用程序快速集成强大的智能对话能力。它支持 Vue 2 和 Vue 3 框架，提供丰富的交互功能和灵活的配置选项，帮助您提升应用的用户体验。

只需简单配置，即可将 AI 小鲸接入您的业务系统，让用户可以通过自然语言交互获取信息、执行操作或获得帮助。

## 核心特性

AI 小鲸具备以下主要特性：

-   **实时对话**：支持流式输出，让 AI 的回复像真实对话一样逐字呈现，更加自然流畅。
-   **内容引用**：用户可以直接在页面上选中文本，快速唤起 AI 小鲸并针对选中内容进行提问或操作。
-   **快捷操作**：支持预设常用的功能或指令（如解释、翻译、总结等），用户选中内容后可一键触发。
-   **预设提示词**：可以预定义常用的提问模板或角色设定，方便用户快速发起特定类型的对话。
-   **可拖拽与缩放界面**：用户可以自由调整 AI 小鲸窗口的位置和大小，适应不同的屏幕和使用场景。
-   **开箱即用**：核心功能只需传入后端 AI 服务接口地址即可启用。
-   **跨框架支持**：提供一致的 API 和体验，同时支持 Vue 2 和 Vue 3 项目。
-   **灵活定制**：支持自定义请求参数、访问会话内容等高级配置。

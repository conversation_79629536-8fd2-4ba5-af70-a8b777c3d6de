{"name": "bk-aidev-agent-frontend", "version": "1.0.1", "private": true, "description": "蓝鲸AI开发者助手前端项目", "scripts": {"dev:component": "pnpm --filter @blueking/ai-blueking dev", "dev:docs": "pnpm --filter ai-blueking-docs dev", "build:component": "pnpm --filter @blueking/ai-blueking build", "build:docs": "pnpm --filter ai-blueking-docs build", "build": "pnpm build:component && pnpm build:docs", "prepare:docs": "pnpm build:component && pnpm dev:docs", "start": "pnpm prepare:docs", "clean": "rm -rf node_modules && pnpm --filter @blueking/ai-blueking clean && pnpm --filter ai-blueking-docs clean"}, "keywords": ["blueking", "ai"], "author": "Tencent <PERSON>", "license": "MIT", "devDependencies": {"@types/node": "^20.14.2", "@types/prompts": "^2.4.9", "@types/semver": "^7.5.8", "@vitejs/plugin-vue": "^5.0.5", "@vitest/ui": "^1.3.1", "bkui-vue": "2.0.2-beta.29", "execa": "^9.2.0", "less": "^4.2.0", "lint-staged": "^15.2.5", "nx": "19.2.1", "picocolors": "^1.0.1", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.1", "prompts": "^2.4.2", "sass": "^1.77.4", "semver": "^7.6.2", "simple-git-hooks": "^2.11.1", "terser": "^5.31.1", "ts-node": "^10.9.2", "tslib": "^2.6.3", "tsx": "^4.12.1", "typescript": "^5.4.5", "vite": "5.4.15", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-css-injected-by-js": "^3.5.1", "vite-plugin-inspect": "^0.8.4", "vitest": "^1.3.1", "vue": "^3.4.27", "vue-tsc": "^2.0.19", "zip-a-folder": "^3.1.6"}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}
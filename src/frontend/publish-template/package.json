{"name": "frontend", "version": "1.0.1-beta.2", "description": "", "main": "index.js", "scripts": {"dev": "bk-cli-service dev", "build": "bk-cli-service build", "server": "node ./server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@blueking/ai-blueking": "1.1.0-multi-session.beta.4", "@blueking/ai-ui-sdk": "0.0.15-beta.30", "@blueking/cli-service": "0.1.0-beta.9", "bkui-vue": "2.0.1", "dayjs": "^1.11.12", "postcss": "~8.4.16", "postcss-import": "^15.0.0", "postcss-mixins": "^9.0.4", "postcss-nested": "^6.0.0", "postcss-nested-ancestors": "^3.0.0", "postcss-preset-env": "^7.8.2", "postcss-simple-vars": "^7.0.0", "postcss-url": "^10.1.3", "quill": "^1.3.7", "vue": "^3.4.33", "vue-router": "^4.5.1", "x-mavon-editor": "^0.0.15"}, "engines": {"node": ">= 16.16.0"}}
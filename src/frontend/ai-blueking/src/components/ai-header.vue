<template>
  <div
    ref="headerRef"
    class="header drag-handle"
    :class="{ 'draggable': props.draggable }"
  >
    <div class="left-section">
      <div class="logo">
        <img
          :src="logo"
          alt="logo"
        />
      </div>
      <div class="title">{{ title }}</div>
    </div>
    <div class="right-section">
      <i
        class="bkai-icon bkai-xinzengliaotian"
        v-bk-tooltips="{ content: t('新增聊天'), boundary: 'parent' }"
        @click="handleNewChat"
      ></i>
      <i
        ref="historyIconRef"
        class="bkai-icon bkai-history"
        v-bk-tooltips="{ content: t('历史会话'), boundary: 'parent' }"
        @click="handleHistoryIconClick"
      ></i>
      <i
        ref="compressionRef"
        class="bkai-icon"
        :class="compressionIcon"
        @click="emit('toggleCompression')"
      ></i>
      <i
        ref="closeRef"
        class="bkai-icon bkai-close-line-2"
        @click="emit('close')"
      ></i>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, onMounted, watch, onBeforeUnmount, h, render } from 'vue';
  import tippy, { Instance } from 'tippy.js';

  import logo from '../assets/images/avatar.png';
  import { useTooltip } from '../composables/use-tippy';
  import { t } from '../lang';
  import { sessionStore } from '../store/sessionStore';

  import { bkTooltips } from 'bkui-vue';
  import HistoryPanel from './history-panel.vue';

  const props = withDefaults(defineProps<{
    title: string;
    isCompressionHeight: boolean;
    draggable: boolean;
  }>(), {
    title: t('AI 小鲸'),
    isCompressionHeight: false,
    draggable: true,
  });

  const emit = defineEmits<(e: 'close' | 'toggleCompression' | 'newChat') => void>();

  const vBkTooltips = bkTooltips;

  // 历史面板相关的 refs
  const historyIconRef = ref<HTMLElement | null>(null);
  const historyPanelInstance = ref<Instance | null>(null);

  const compressionIcon = computed(() => {
    return props.isCompressionHeight ? 'bkai-morenchicun' : 'bkai-yasuo';
  });

  const compressionTooltip = computed(() => {
    return props.isCompressionHeight ? t('恢复默认尺寸') : t('缩小高度');
  });

  const headerRef = ref<HTMLElement | null>(null);
  const compressionRef = ref<HTMLElement | null>(null);
  const closeRef = ref<HTMLElement | null>(null);

  const { createTooltip, destroyAll } = useTooltip({
    arrow: true,
    delay: [0, 0],
  });

  // 初始化 tooltip
  const initTooltips = () => {
    destroyAll(); // 先清除所有已存在的 tooltip
    if (compressionRef.value) {
      createTooltip(compressionRef.value, compressionTooltip.value, {
        appendTo: document.querySelector('.ai-blueking-container-wrapper') as HTMLElement,
      });
    }
    if (closeRef.value) {
      createTooltip(closeRef.value, t('关闭'), {
        appendTo: document.querySelector('.ai-blueking-container-wrapper') as HTMLElement,
      });
    }
  };

  // 监听压缩状态变化，更新 tooltip
  watch(
    () => props.isCompressionHeight,
    () => {
      initTooltips();
    },
  );

  onMounted(() => {
    initTooltips();
  });

  onBeforeUnmount(() => {
    destroyAll();
    // 清理历史面板 tippy 实例和挂载点
    if (historyPanelInstance.value) {
      historyPanelInstance.value.destroy();
      historyPanelInstance.value = null;
    }
    // 清理挂载点
    if (historyMountPoint) {
      render(null, historyMountPoint);
      historyMountPoint = null;
    }
  });

  // 历史面板的挂载点，保持引用避免重复创建
  let historyMountPoint: HTMLElement | null = null;

  // 创建历史面板内容
  const createHistoryPanelContent = () => {
    if (!historyMountPoint) {
      historyMountPoint = document.createElement('div');
    }

    // 每次都重新渲染组件内容
    const vnode = h(HistoryPanel, {
      onClose: handleCloseHistoryPanel
    });
    render(vnode, historyMountPoint);

    return historyMountPoint;
  };

  // 创建历史面板的 tippy 实例
  const createHistoryPanel = () => {
    if (!historyIconRef.value) return;

    // 创建 tippy 实例
    historyPanelInstance.value = tippy(historyIconRef.value, {
      content: createHistoryPanelContent(),
      interactive: true,
      trigger: 'manual',
      theme: 'light',
      placement: 'bottom-end',
      arrow: false,
      appendTo: document.querySelector('.ai-blueking-container-wrapper') || document.body,
      hideOnClick: true, // 点击外部自动隐藏
      animation: 'scale',
      duration: 200,
      delay: [0, 0],
      offset: [0, 8],
      // 每次显示前更新内容
      onShow: () => {
        if (historyPanelInstance.value && historyMountPoint) {
          historyPanelInstance.value.setContent(createHistoryPanelContent());
        }
        // 添加全局点击监听器
        setTimeout(() => {
          document.addEventListener('click', handleDocumentClick, true);
        }, 0);
      },
      // 隐藏时移除全局点击监听器
      onHidden: () => {
        document.removeEventListener('click', handleDocumentClick, true);
      },
    });
  };

  // 处理文档点击事件
  const handleDocumentClick = (event: Event) => {
    const target = event.target as Element;
    const historyIcon = historyIconRef.value;
    const tippyBox = document.querySelector('[data-tippy-root]');

    // 如果点击的是历史图标或者 tippy 内容，不关闭面板
    if (historyIcon && (historyIcon.contains(target) || (tippyBox && tippyBox.contains(target)))) {
      return;
    }

    // 否则关闭面板
    if (historyPanelInstance.value?.state.isVisible) {
      historyPanelInstance.value.hide();
    }
  };

  // 处理历史图标点击
  const handleHistoryIconClick = (event: Event) => {
    // 阻止事件冒泡，避免触发外部点击隐藏
    event.stopPropagation();

    if (!historyPanelInstance.value) {
      createHistoryPanel();
    }

    // 切换显示/隐藏状态
    if (historyPanelInstance.value?.state.isVisible) {
      historyPanelInstance.value.hide();
    } else {
      historyPanelInstance.value?.show();
    }
  };

  // 关闭历史面板
  const handleCloseHistoryPanel = () => {
    historyPanelInstance.value?.hide();
  };

  // 处理新增聊天按钮点击
  const handleNewChat = async () => {
    try {
      // 通知父组件
      emit('newChat')
      // 创建新会话
      await sessionStore.initSession(false)
    } catch (error) {
      console.error('Failed to create new chat:', error)
    }
  }
</script>

<style lang="scss" scoped>
  .header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    padding: 14px;
    border-bottom: 1px solid #e5e5e5;

    &.draggable {
      cursor: move;
    }

    .left-section {
      display: flex;
      gap: 4px;
      align-items: center;

      .logo {
        width: 32px;
        height: 32px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .title {
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        color: #4d4f56;
      }
    }

    .right-section {
      display: flex;
      gap: 12px;
    }

    .bkai-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      margin-right: 0;
      font-size: 14px;
      color: #63656e;
      cursor: pointer;
      border-radius: 2px;

      &:hover {
        color: #4d4f56;
        background: #eaebf0;
      }
    }
  }
</style>

<style lang="scss">
  // 历史面板 tippy 样式
  .tippy-box[data-theme~='light'] {
    background-color: #fff;
    border: 1px solid #dcdee5;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 0;

    .tippy-content {
      padding: 0;
    }
  }
</style>
